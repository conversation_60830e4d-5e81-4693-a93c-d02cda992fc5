/**
 * @file base_config.h
 * @brief TIMO智能闹钟底座设备配置
 * @version 1.0.0
 * @date 2025-06-27
 * 
 * 底座硬件配置：
 * - ESP32-C2 (ESP8684H2) 主控
 * - WS2812 RGB灯带 (30颗LED)
 * - 声音传感器
 * - 用户按键
 * - LED连接指示灯
 */

#ifndef BASE_CONFIG_H
#define BASE_CONFIG_H

#include "driver/gpio.h"
#include "driver/adc.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ========== GPIO引脚定义 ========== */

/* 声音传感器 */
#define SOUND_SENSOR_ADC_CHANNEL    ADC1_CHANNEL_0  // GPIO0
#define SOUND_SENSOR_ADC_UNIT       ADC_UNIT_1
#define SOUND_SENSOR_ADC_ATTEN      ADC_ATTEN_DB_11

/* WS2812 RGB灯带 */
#define WS2812_GPIO                 8
#define WS2812_LED_COUNT            30
#define WS2812_RMT_CHANNEL          RMT_CHANNEL_0

/* 用户按键 */
#define USER_BUTTON_GPIO            9
#define USER_BUTTON_ACTIVE_LEVEL    0   // 按下为低电平

/* LED连接指示灯 */
#define STATUS_LED_GPIO             18

/* ========== 系统配置 ========== */

/* 蓝牙配置 */
#define BT_DEVICE_NAME              "TIMO_Base"
#define BT_SERVICE_UUID             "12345678-1234-1234-1234-123456789abc"
#define BT_CHAR_UUID_TX             "*************-4321-4321-cba987654321"
#define BT_CHAR_UUID_RX             "11111111-**************-************"

/* 声音传感器配置 */
#define SOUND_THRESHOLD_LOW         100     // 低音量阈值
#define SOUND_THRESHOLD_MEDIUM      500     // 中音量阈值
#define SOUND_THRESHOLD_HIGH        1000    // 高音量阈值
#define SOUND_SAMPLE_RATE_MS        50      // 采样间隔(毫秒)

/* 氛围灯效配置 */
#define AMBIENT_UPDATE_RATE_MS      50      // 灯效更新间隔(毫秒)
#define AMBIENT_BRIGHTNESS_MAX      255     // 最大亮度
#define AMBIENT_BRIGHTNESS_MIN      10      // 最小亮度

/* 任务优先级 */
#define TASK_PRIORITY_HIGH          (configMAX_PRIORITIES - 1)
#define TASK_PRIORITY_NORMAL        (configMAX_PRIORITIES - 2)
#define TASK_PRIORITY_LOW           (configMAX_PRIORITIES - 3)

/* 任务堆栈大小 */
#define TASK_STACK_SIZE_LARGE       (4096)
#define TASK_STACK_SIZE_MEDIUM      (2048)
#define TASK_STACK_SIZE_SMALL       (1024)

/* ========== 氛围场景定义 ========== */

typedef enum {
    AMBIENT_SCENE_OFF = 0,          // 关闭
    AMBIENT_SCENE_STANDBY,          // 待机模式
    AMBIENT_SCENE_CONVERSATION,     // 对话律动
    AMBIENT_SCENE_MORNING_WAKE,     // 晨间唤醒
    AMBIENT_SCENE_NAP_WAKE,         // 小憩唤醒
    AMBIENT_SCENE_SLEEP_AID,        // 助眠模式
    AMBIENT_SCENE_TODO_REMIND,      // 待办提醒
    AMBIENT_SCENE_ALERT_LOW,        // 低级预警
    AMBIENT_SCENE_ALERT_MEDIUM,     // 中级预警
    AMBIENT_SCENE_ALERT_HIGH,       // 高级预警
    AMBIENT_SCENE_FOCUS_MODE,       // 专注模式
    AMBIENT_SCENE_CHARGING,         // 充电状态
    AMBIENT_SCENE_PAIRING,          // 配对模式
    AMBIENT_SCENE_MAX
} ambient_scene_t;

/* ========== 颜色定义 ========== */

typedef struct {
    uint8_t r;
    uint8_t g;
    uint8_t b;
} rgb_color_t;

/* 预定义颜色 */
#define COLOR_OFF           {0, 0, 0}
#define COLOR_WHITE         {255, 255, 255}
#define COLOR_RED           {255, 0, 0}
#define COLOR_GREEN         {0, 255, 0}
#define COLOR_BLUE          {0, 0, 255}
#define COLOR_YELLOW        {255, 255, 0}
#define COLOR_CYAN          {0, 255, 255}
#define COLOR_MAGENTA       {255, 0, 255}
#define COLOR_ORANGE        {255, 165, 0}
#define COLOR_PURPLE        {128, 0, 128}
#define COLOR_WARM_WHITE    {255, 220, 180}
#define COLOR_COOL_WHITE    {180, 220, 255}

/* ========== 系统状态定义 ========== */

typedef struct {
    bool bluetooth_connected;
    bool main_device_online;
    bool charging_active;
    ambient_scene_t current_scene;
    uint8_t brightness;
    uint16_t sound_level;
    bool user_button_pressed;
} base_system_status_t;

/* ========== 函数声明 ========== */

/**
 * @brief 获取系统状态
 * @return base_system_status_t* 
 */
base_system_status_t* base_get_system_status(void);

/**
 * @brief 设置状态LED
 * @param on true=点亮, false=熄灭
 */
void base_set_status_led(bool on);

/**
 * @brief 切换状态LED
 */
void base_toggle_status_led(void);

#ifdef __cplusplus
}
#endif

#endif /* BASE_CONFIG_H */
