# TIMO智能闹钟开发指南

## 开发环境搭建

### 1. 固件开发环境 (ESP32)

#### 安装ESP-IDF
```bash
# 克隆ESP-IDF仓库
git clone --recursive https://github.com/espressif/esp-idf.git
cd esp-idf

# 安装工具链 (Windows)
install.bat

# 安装工具链 (Linux/macOS)
./install.sh

# 设置环境变量 (Windows)
export.bat

# 设置环境变量 (Linux/macOS)
. ./export.sh
```

#### VS Code配置
1. 安装ESP-IDF插件
2. 配置ESP-IDF路径
3. 设置工具链路径
4. 配置串口和调试器

### 2. 云端服务开发环境

#### 安装Node.js
```bash
# 使用nvm安装Node.js (推荐)
nvm install 18
nvm use 18

# 或直接下载安装
# https://nodejs.org/
```

#### 安装MongoDB
```bash
# 使用Docker (推荐)
docker run -d -p 27017:27017 --name mongodb mongo:latest

# 或本地安装
# https://www.mongodb.com/try/download/community
```

#### 安装Redis
```bash
# 使用Docker (推荐)
docker run -d -p 6379:6379 --name redis redis:latest

# 或本地安装
# https://redis.io/download
```

### 3. 微信小程序开发环境

#### 安装微信开发者工具
1. 下载微信开发者工具
2. 注册微信小程序账号
3. 获取AppID
4. 配置开发环境

## 开发流程

### 1. 主体设备固件开发

#### 编译和烧录
```bash
cd 1_main_device_firmware

# 设置目标芯片
idf.py set-target esp32s3

# 配置项目
idf.py menuconfig

# 编译项目
idf.py build

# 烧录固件
idf.py flash

# 监控串口输出
idf.py monitor

# 一键编译烧录监控
idf.py flash monitor
```

#### 调试方法
```bash
# 使用GDB调试
idf.py gdb

# 查看内存使用
idf.py size

# 生成分析报告
idf.py size-components
idf.py size-files
```

### 2. 底座设备固件开发

#### 编译和烧录
```bash
cd 2_base_station_firmware

# 设置目标芯片
idf.py set-target esp32c2

# 编译烧录
idf.py build flash monitor
```

### 3. 云端服务开发

#### 启动开发服务器
```bash
cd 3_cloud_service

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 运行测试
npm test

# 代码检查
npm run lint

# 格式化代码
npm run format
```

#### 数据库操作
```bash
# 连接MongoDB
mongo mongodb://localhost:27017/timo

# 连接Redis
redis-cli
```

### 4. 微信小程序开发

#### 开发流程
1. 使用微信开发者工具打开项目
2. 配置AppID和服务器域名
3. 开发和调试
4. 真机预览测试
5. 上传代码审核
6. 发布上线

## 代码规范

### 1. C/C++ 代码规范 (固件)

#### 命名规范
```c
// 宏定义：全大写，下划线分隔
#define MAX_BUFFER_SIZE 1024
#define GPIO_LED_PIN    2

// 函数名：小写，下划线分隔
esp_err_t sensor_init(void);
void led_set_brightness(uint8_t brightness);

// 变量名：小写，下划线分隔
static bool g_system_initialized = false;
uint16_t sensor_data[10];

// 结构体：小写，下划线分隔，_t后缀
typedef struct {
    uint8_t temperature;
    uint8_t humidity;
} sensor_data_t;
```

#### 注释规范
```c
/**
 * @brief 初始化传感器系统
 * @param config 传感器配置参数
 * @return esp_err_t 
 *         - ESP_OK: 成功
 *         - ESP_FAIL: 失败
 */
esp_err_t sensor_system_init(const sensor_config_t *config);
```

### 2. JavaScript 代码规范 (云端)

#### 命名规范
```javascript
// 变量和函数：驼峰命名
const deviceManager = new DeviceManager();
function getUserProfile(userId) { }

// 常量：全大写，下划线分隔
const MAX_RETRY_COUNT = 3;
const API_BASE_URL = 'https://api.timo.com';

// 类名：帕斯卡命名
class DeviceController { }
```

#### ESLint配置
```json
{
  "extends": ["standard"],
  "rules": {
    "indent": ["error", 2],
    "quotes": ["error", "single"],
    "semi": ["error", "never"]
  }
}
```

### 3. 微信小程序代码规范

#### 文件命名
```
pages/device-control/device-control.js
components/data-chart/data-chart.js
utils/bluetooth-helper.js
```

#### 代码风格
```javascript
// 页面生命周期
Page({
  data: {
    deviceList: [],
    isConnected: false
  },

  onLoad() {
    this.initBluetooth()
  },

  // 方法命名：驼峰命名
  initBluetooth() {
    // 实现代码
  }
})
```

## 测试策略

### 1. 固件测试

#### 单元测试
```c
// 使用Unity测试框架
#include "unity.h"

void test_sensor_init(void) {
    esp_err_t ret = sensor_init();
    TEST_ASSERT_EQUAL(ESP_OK, ret);
}

void app_main(void) {
    UNITY_BEGIN();
    RUN_TEST(test_sensor_init);
    UNITY_END();
}
```

#### 硬件在环测试
- 使用真实硬件进行测试
- 模拟各种传感器输入
- 验证输出信号正确性

### 2. 云端服务测试

#### 单元测试
```javascript
const request = require('supertest');
const app = require('../src/app');

describe('Device API', () => {
  test('POST /api/device/register', async () => {
    const response = await request(app)
      .post('/api/device/register')
      .send({ deviceId: 'test123' });
    
    expect(response.status).toBe(200);
  });
});
```

#### 集成测试
- API接口测试
- 数据库操作测试
- 第三方服务集成测试

### 3. 小程序测试

#### 功能测试
- 页面跳转测试
- 数据绑定测试
- 事件处理测试

#### 兼容性测试
- 不同机型测试
- 不同微信版本测试
- 网络环境测试

## 部署指南

### 1. 固件部署

#### 生产固件构建
```bash
# 设置生产配置
idf.py menuconfig

# 构建发布版本
idf.py build

# 生成固件包
esptool.py --chip esp32s3 merge_bin -o firmware.bin \
  0x0 bootloader.bin \
  0x8000 partition-table.bin \
  0x10000 app.bin
```

### 2. 云端服务部署

#### Docker部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

```bash
# 构建镜像
docker build -t timo-cloud-service .

# 运行容器
docker run -d -p 3000:3000 timo-cloud-service
```

### 3. 小程序发布

#### 发布流程
1. 代码审查和测试
2. 版本号更新
3. 上传代码到微信后台
4. 提交审核
5. 审核通过后发布

## 故障排除

### 1. 固件开发问题

#### 编译错误
- 检查ESP-IDF版本
- 确认依赖组件
- 查看错误日志

#### 运行时错误
- 使用串口监控
- 检查内存使用
- 分析崩溃日志

### 2. 云端服务问题

#### 服务启动失败
- 检查端口占用
- 确认数据库连接
- 查看错误日志

#### 性能问题
- 监控CPU和内存使用
- 分析数据库查询
- 优化代码逻辑

### 3. 小程序问题

#### 调试方法
- 使用开发者工具调试
- 查看控制台日志
- 真机调试测试

## 版本控制

### Git工作流
1. 从main分支创建功能分支
2. 在功能分支上开发
3. 提交Pull Request
4. 代码审查
5. 合并到main分支

### 提交规范
```
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建工具或辅助工具的变动
```
