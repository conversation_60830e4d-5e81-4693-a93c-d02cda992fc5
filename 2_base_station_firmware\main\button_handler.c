/**
 * @file button_handler.c
 * @brief 按键处理模块实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "base_config.h"
#include "esp_log.h"
#include "esp_err.h"
#include "driver/gpio.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"

static const char *TAG = "BUTTON";

/* 按键状态 */
typedef enum {
    BUTTON_STATE_RELEASED = 0,
    BUTTON_STATE_PRESSED,
    BUTTON_STATE_LONG_PRESSED
} button_state_t;

/* 按键事件 */
typedef enum {
    BUTTON_EVENT_NONE = 0,
    BUTTON_EVENT_PRESS,
    BUTTON_EVENT_RELEASE,
    BUTTON_EVENT_LONG_PRESS,
    BUTTON_EVENT_LONG_RELEASE
} button_event_type_t;

/* 按键数据结构 */
typedef struct {
    button_state_t state;
    button_state_t last_state;
    uint32_t press_time;
    uint32_t release_time;
    uint32_t debounce_time;
    bool long_press_triggered;
} button_data_t;

/* 全局变量 */
static bool g_button_initialized = false;
static bool g_button_running = false;
static button_data_t g_button_data = {0};
static QueueHandle_t g_button_event_queue = NULL;

/* 按键事件结构体 */
typedef struct {
    button_event_type_t type;
    uint32_t timestamp;
    uint32_t duration;
} button_event_t;

/**
 * @brief GPIO中断处理函数
 */
static void IRAM_ATTR button_isr_handler(void* arg)
{
    uint32_t current_time = esp_timer_get_time() / 1000;
    
    // 读取按键状态
    int level = gpio_get_level(USER_BUTTON_GPIO);
    bool pressed = (level == USER_BUTTON_ACTIVE_LEVEL);
    
    // 防抖处理
    if (current_time - g_button_data.debounce_time < 50) {
        return;
    }
    g_button_data.debounce_time = current_time;
    
    // 状态变化处理
    if (pressed && g_button_data.state == BUTTON_STATE_RELEASED) {
        g_button_data.state = BUTTON_STATE_PRESSED;
        g_button_data.press_time = current_time;
        g_button_data.long_press_triggered = false;
        
        // 发送按下事件
        button_event_t event = {
            .type = BUTTON_EVENT_PRESS,
            .timestamp = current_time,
            .duration = 0
        };
        xQueueSendFromISR(g_button_event_queue, &event, NULL);
        
    } else if (!pressed && g_button_data.state != BUTTON_STATE_RELEASED) {
        uint32_t press_duration = current_time - g_button_data.press_time;
        
        g_button_data.state = BUTTON_STATE_RELEASED;
        g_button_data.release_time = current_time;
        
        // 发送释放事件
        button_event_t event = {
            .type = g_button_data.long_press_triggered ? BUTTON_EVENT_LONG_RELEASE : BUTTON_EVENT_RELEASE,
            .timestamp = current_time,
            .duration = press_duration
        };
        xQueueSendFromISR(g_button_event_queue, &event, NULL);
    }
}

/**
 * @brief 按键监控任务
 */
static void button_monitor_task(void *pvParameters)
{
    ESP_LOGI(TAG, "按键监控任务启动");
    
    while (g_button_running) {
        uint32_t current_time = esp_timer_get_time() / 1000;
        
        // 检查长按
        if (g_button_data.state == BUTTON_STATE_PRESSED && !g_button_data.long_press_triggered) {
            uint32_t press_duration = current_time - g_button_data.press_time;
            
            if (press_duration >= 1000) { // 1秒长按
                g_button_data.state = BUTTON_STATE_LONG_PRESSED;
                g_button_data.long_press_triggered = true;
                
                // 发送长按事件
                button_event_t event = {
                    .type = BUTTON_EVENT_LONG_PRESS,
                    .timestamp = current_time,
                    .duration = press_duration
                };
                xQueueSend(g_button_event_queue, &event, 0);
                
                ESP_LOGI(TAG, "检测到长按事件");
            }
        }
        
        vTaskDelay(pdMS_TO_TICKS(10));
    }
    
    vTaskDelete(NULL);
}

/**
 * @brief 初始化按键处理器
 */
esp_err_t button_handler_init(void)
{
    if (g_button_initialized) {
        ESP_LOGW(TAG, "按键处理器已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化按键处理器...");
    
    // 创建事件队列
    g_button_event_queue = xQueueCreate(10, sizeof(button_event_t));
    if (g_button_event_queue == NULL) {
        ESP_LOGE(TAG, "创建按键事件队列失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 配置GPIO
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_ANYEDGE,
        .mode = GPIO_MODE_INPUT,
        .pin_bit_mask = (1ULL << USER_BUTTON_GPIO),
        .pull_down_en = 0,
        .pull_up_en = 1
    };
    
    esp_err_t ret = gpio_config(&io_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "配置按键GPIO失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 安装GPIO中断服务
    ret = gpio_install_isr_service(0);
    if (ret != ESP_OK && ret != ESP_ERR_INVALID_STATE) {
        ESP_LOGE(TAG, "安装GPIO中断服务失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 添加中断处理函数
    ret = gpio_isr_handler_add(USER_BUTTON_GPIO, button_isr_handler, NULL);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "添加按键中断处理函数失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 初始化按键数据
    memset(&g_button_data, 0, sizeof(g_button_data));
    g_button_data.state = BUTTON_STATE_RELEASED;
    g_button_data.last_state = BUTTON_STATE_RELEASED;
    
    g_button_initialized = true;
    ESP_LOGI(TAG, "按键处理器初始化完成");
    return ESP_OK;
}

/**
 * @brief 启动按键处理器
 */
esp_err_t button_handler_start(void)
{
    if (!g_button_initialized) {
        ESP_LOGE(TAG, "按键处理器未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    g_button_running = true;
    
    // 创建按键监控任务
    BaseType_t ret = xTaskCreate(
        button_monitor_task,
        "button_monitor",
        TASK_STACK_SIZE_SMALL,
        NULL,
        TASK_PRIORITY_NORMAL,
        NULL
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建按键监控任务失败");
        g_button_running = false;
        return ESP_ERR_NO_MEM;
    }
    
    ESP_LOGI(TAG, "按键处理器启动");
    return ESP_OK;
}

/**
 * @brief 停止按键处理器
 */
esp_err_t button_handler_stop(void)
{
    g_button_running = false;
    ESP_LOGI(TAG, "按键处理器停止");
    return ESP_OK;
}

/**
 * @brief 获取按键事件
 */
bool button_handler_get_event(button_event_t *event)
{
    if (!g_button_initialized || !event) {
        return false;
    }
    
    return xQueueReceive(g_button_event_queue, event, 0) == pdTRUE;
}

/**
 * @brief 等待按键事件
 */
bool button_handler_wait_event(button_event_t *event, uint32_t timeout_ms)
{
    if (!g_button_initialized || !event) {
        return false;
    }
    
    return xQueueReceive(g_button_event_queue, event, pdMS_TO_TICKS(timeout_ms)) == pdTRUE;
}

/**
 * @brief 获取当前按键状态
 */
bool button_handler_is_pressed(void)
{
    if (!g_button_initialized) {
        return false;
    }
    
    return (g_button_data.state != BUTTON_STATE_RELEASED);
}

/**
 * @brief 获取按键按下时长
 */
uint32_t button_handler_get_press_duration(void)
{
    if (!g_button_initialized || g_button_data.state == BUTTON_STATE_RELEASED) {
        return 0;
    }
    
    uint32_t current_time = esp_timer_get_time() / 1000;
    return current_time - g_button_data.press_time;
}

/**
 * @brief 清空按键事件队列
 */
void button_handler_clear_events(void)
{
    if (!g_button_initialized) {
        return;
    }
    
    button_event_t dummy;
    while (xQueueReceive(g_button_event_queue, &dummy, 0) == pdTRUE) {
        // 清空队列
    }
}

/**
 * @brief 获取按键统计信息
 */
void button_handler_get_stats(uint32_t *total_presses, uint32_t *long_presses)
{
    static uint32_t s_total_presses = 0;
    static uint32_t s_long_presses = 0;
    
    // 这里可以添加统计逻辑
    
    if (total_presses) {
        *total_presses = s_total_presses;
    }
    
    if (long_presses) {
        *long_presses = s_long_presses;
    }
}
